#!/usr/bin/env python3
"""
简化的音乐流派分类测试脚本
用于验证代码功能并生成基本结果
"""

import os
import sys
import subprocess
import time

def check_environment():
    """检查运行环境"""
    print("检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("警告：建议使用Python 3.7或更高版本")
    
    # 检查必要的包
    required_packages = {
        'numpy': 'numpy',
        'pandas': 'pandas',
        'sklearn': 'scikit-learn',
        'yaml': 'PyYAML',
        'tensorflow': 'tensorflow',
        'keras_tuner': 'keras-tuner'
    }
    
    missing_packages = []
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (需要安装: pip install {pip_name})")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n请安装缺失的包：")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_project():
    """设置项目"""
    print("\n设置项目...")
    
    # 检查项目目录
    if not os.path.exists('music-genre-classifier-main'):
        print("错误：未找到 music-genre-classifier-main 目录")
        return False
    
    # 切换到项目目录
    os.chdir('music-genre-classifier-main')
    
    # 检查项目文件
    required_files = ['setup.py', 'pyproject.toml', 'configs/default.yaml']
    for file in required_files:
        if not os.path.exists(file):
            print(f"警告：未找到 {file}")
    
    # 安装项目
    try:
        print("安装项目...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-e', '.'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ 项目安装成功")
        else:
            print(f"安装失败：{result.stderr}")
            return False
    except Exception as e:
        print(f"安装过程中出错：{e}")
        return False
    
    return True

def check_local_dataset():
    """检查本地数据集"""
    print("\n检查本地数据集...")

    # 检查数据目录
    data_dir = "data"
    if not os.path.exists(data_dir):
        print(f"✗ 未找到数据目录：{data_dir}")
        print("\n请按照以下步骤准备数据集：")
        print("1. 下载GTZAN数据集：")
        print("   - 链接1：http://marsyas.info/downloads/datasets.html")
        print("   - 链接2：https://www.kaggle.com/datasets/andradaolteanu/gtzan-dataset-music-genre-classification")
        print("2. 创建目录结构：")
        print("   data/")
        print("   └── Data/")
        print("       ├── features_30_sec.csv")
        print("       └── features_3_sec.csv")
        print("3. 将下载的CSV文件放到 data/Data/ 目录下")
        return False

    # 检查必要的文件
    required_files = [
        "data/Data/features_30_sec.csv",
        "data/Data/features_3_sec.csv"
    ]

    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            missing_files.append(file_path)

    if missing_files:
        print(f"\n缺失文件：{len(missing_files)}个")
        print("请确保数据集文件已正确放置")
        return False

    print("✓ 本地数据集检查通过")
    return True

def run_simple_test():
    """运行简单测试"""
    print("\n运行简单测试...")
    
    try:
        # 测试导入
        from music_genre_classifier import dataset, models
        print("✓ 模块导入成功")
        
        # 测试配置文件加载
        import yaml
        with open('configs/default.yaml', 'r') as f:
            config = yaml.load(f, Loader=yaml.Loader)
        print("✓ 配置文件加载成功")
        
        print(f"数据集配置：{config['dataset']['gtzan_url']}")
        print(f"特征数量：{len(config['dataset']['features'])}")
        print(f"模型数量：{len(config['models'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败：{e}")
        return False

def run_knn_only():
    """运行KNN模型"""
    print("\n运行KNN模型...")
    
    try:
        cmd = [sys.executable, '-m', 'music_genre_classifier', 
               'configs/knn_only.yaml', '--display_results']
        
        print("执行命令：", ' '.join(cmd))
        
        # 设置超时时间（10分钟）
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✓ KNN模型运行成功")
            print("输出：")
            print(result.stdout)
            return True
        else:
            print(f"✗ KNN模型运行失败：{result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ KNN模型运行超时")
        return False
    except Exception as e:
        print(f"✗ 运行KNN模型时出错：{e}")
        return False

def run_neural_net_only():
    """运行神经网络模型"""
    print("\n运行神经网络模型...")
    
    try:
        cmd = [sys.executable, '-m', 'music_genre_classifier', 
               'configs/neural_net_only.yaml', '--display_results']
        
        print("执行命令：", ' '.join(cmd))
        
        # 设置超时时间（20分钟）
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1200)
        
        if result.returncode == 0:
            print("✓ 神经网络模型运行成功")
            print("输出：")
            print(result.stdout)
            return True
        else:
            print(f"✗ 神经网络模型运行失败：{result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 神经网络模型运行超时")
        return False
    except Exception as e:
        print(f"✗ 运行神经网络模型时出错：{e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("音乐流派分类器测试脚本")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n请先安装必要的依赖包")
        return
    
    # 设置项目
    if not setup_project():
        print("\n项目设置失败")
        return
    
    # 检查本地数据集
    dataset_ok = check_local_dataset()

    # 运行简单测试
    if not run_simple_test():
        print("\n基础测试失败")
        return

    if not dataset_ok:
        print("\n由于本地数据集未准备好，无法运行实验")
        print("请按照提示准备数据集后重新运行")
        return
    
    # 询问用户要运行哪个模型
    print("\n选择要运行的模型：")
    print("1. 仅KNN模型（较快）")
    print("2. 仅神经网络模型（较慢）")
    print("3. 两个模型都运行")
    print("4. 跳过模型训练")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == '1':
        run_knn_only()
    elif choice == '2':
        run_neural_net_only()
    elif choice == '3':
        run_knn_only()
        run_neural_net_only()
    elif choice == '4':
        print("跳过模型训练")
    else:
        print("无效选择，跳过模型训练")
    
    print("\n测试完成！")
    print("如果遇到问题，请检查：")
    print("1. Python环境和依赖包")
    print("2. Kaggle API配置")
    print("3. 网络连接（下载数据集需要）")

if __name__ == "__main__":
    main()
