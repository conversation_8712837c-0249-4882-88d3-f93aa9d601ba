#!/usr/bin/env python3
"""
中文版音乐流派分类实验脚本
直接使用scikit-learn进行KNN和简单神经网络实验
所有注释和输出信息使用中文
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载和预处理数据"""
    print("正在加载数据...")
    
    # 加载数据
    df = pd.read_csv('data/Data/features_30_sec.csv')
    print(f"数据形状: {df.shape}")
    print(f"流派分布: {df['label'].value_counts().to_dict()}")
    
    # 分离特征和标签
    X = df.drop('label', axis=1).values
    y = df['label'].values
    
    # 标签编码
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)
    
    print(f"特征维度: {X.shape}")
    print(f"类别数量: {len(le.classes_)}")
    
    # 流派中文名称映射
    genre_chinese = {
        'blues': '蓝调', 'classical': '古典', 'country': '乡村', 
        'disco': '迪斯科', 'hiphop': '嘻哈', 'jazz': '爵士',
        'metal': '金属', 'pop': '流行', 'reggae': '雷鬼', 'rock': '摇滚'
    }
    
    chinese_classes = [genre_chinese.get(cls, cls) for cls in le.classes_]
    print(f"音乐流派: {chinese_classes}")
    
    return X, y_encoded, le, genre_chinese

def split_and_scale_data(X, y):
    """数据划分和标准化"""
    print("\n正在进行数据划分和标准化...")
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    
    # 特征标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print("特征标准化完成")
    
    return X_train_scaled, X_test_scaled, y_train, y_test, scaler

def train_knn_model(X_train, X_test, y_train, y_test):
    """训练KNN模型"""
    print("\n" + "=" * 50)
    print("开始训练KNN模型")
    print("=" * 50)
    
    # 超参数搜索
    print("正在进行超参数搜索...")
    param_grid = {
        'n_neighbors': [3, 5, 7, 9, 11],
        'weights': ['uniform', 'distance'],
        'metric': ['euclidean', 'manhattan']
    }
    
    knn = KNeighborsClassifier()
    grid_search = GridSearchCV(knn, param_grid, cv=5, scoring='accuracy', n_jobs=-1)
    grid_search.fit(X_train, y_train)
    
    print(f"最佳参数: {grid_search.best_params_}")
    print(f"最佳交叉验证分数: {grid_search.best_score_:.4f}")
    
    # 在测试集上评估
    best_knn = grid_search.best_estimator_
    y_pred = best_knn.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    return best_knn, y_pred, accuracy

def train_mlp_model(X_train, X_test, y_train, y_test):
    """训练多层感知机模型"""
    print("\n" + "=" * 50)
    print("开始训练神经网络模型")
    print("=" * 50)
    
    # 超参数搜索
    print("正在进行超参数搜索...")
    param_grid = {
        'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
        'learning_rate_init': [0.001, 0.01, 0.1],
        'alpha': [0.0001, 0.001, 0.01]
    }
    
    mlp = MLPClassifier(max_iter=500, random_state=42)
    grid_search = GridSearchCV(mlp, param_grid, cv=3, scoring='accuracy', n_jobs=-1)
    grid_search.fit(X_train, y_train)
    
    print(f"最佳参数: {grid_search.best_params_}")
    print(f"最佳交叉验证分数: {grid_search.best_score_:.4f}")
    
    # 在测试集上评估
    best_mlp = grid_search.best_estimator_
    y_pred = best_mlp.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    return best_mlp, y_pred, accuracy

def generate_results_visualization(y_test, y_pred_knn, y_pred_mlp, le, genre_chinese,
                                 knn_accuracy, mlp_accuracy):
    """生成实验结果可视化"""
    print("\n正在生成实验结果可视化...")
    
    # 创建结果目录
    import os
    os.makedirs("中文实验结果", exist_ok=True)
    
    # 1. 模型准确率对比
    plt.figure(figsize=(10, 6))
    models = ['KNN模型', '神经网络模型']
    accuracies = [knn_accuracy * 100, mlp_accuracy * 100]
    
    bars = plt.bar(models, accuracies, color=['skyblue', 'lightcoral'], alpha=0.8)
    plt.title('模型性能对比（实验结果）', fontsize=16, fontweight='bold')
    plt.ylabel('准确率 (%)', fontsize=12)
    plt.ylim(0, 100)
    
    # 添加数值标签
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('中文实验结果/模型性能对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. KNN混淆矩阵
    plt.figure(figsize=(10, 8))
    cm_knn = confusion_matrix(y_test, y_pred_knn)
    
    # 获取中文流派名称
    chinese_labels = [genre_chinese.get(cls, cls) for cls in le.classes_]
    
    sns.heatmap(cm_knn, annot=True, fmt='d', cmap='Blues',
                xticklabels=chinese_labels, yticklabels=chinese_labels,
                cbar_kws={'label': '样本数量'})
    plt.title('KNN模型混淆矩阵', fontsize=16, fontweight='bold')
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('中文实验结果/KNN混淆矩阵.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 神经网络混淆矩阵
    plt.figure(figsize=(10, 8))
    cm_mlp = confusion_matrix(y_test, y_pred_mlp)
    sns.heatmap(cm_mlp, annot=True, fmt='d', cmap='Blues',
                xticklabels=chinese_labels, yticklabels=chinese_labels,
                cbar_kws={'label': '样本数量'})
    plt.title('神经网络模型混淆矩阵', fontsize=16, fontweight='bold')
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('中文实验结果/神经网络混淆矩阵.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("可视化图表已保存到 中文实验结果/ 目录")

def generate_detailed_report(y_test, y_pred_knn, y_pred_mlp, le, genre_chinese,
                           knn_accuracy, mlp_accuracy):
    """生成详细的实验报告"""
    print("\n正在生成详细实验报告...")
    
    # 获取中文流派名称
    chinese_labels = [genre_chinese.get(cls, cls) for cls in le.classes_]
    
    report = f"""
# 音乐流派分类实验报告

## 实验设置
- 数据集：GTZAN音乐流派数据集
- 特征数量：57维音频特征
- 样本数量：1000个（每个流派100个）
- 训练集：800个样本
- 测试集：200个样本

## 音乐流派
{', '.join(chinese_labels)}

## 实验结果

### KNN模型
- 测试准确率：{knn_accuracy:.4f} ({knn_accuracy*100:.2f}%)

### 神经网络模型
- 测试准确率：{mlp_accuracy:.4f} ({mlp_accuracy*100:.2f}%)

### 性能提升
- 神经网络相比KNN提升：{(mlp_accuracy - knn_accuracy)*100:.2f}个百分点

## 分类报告

### KNN分类报告
{classification_report(y_test, y_pred_knn, target_names=chinese_labels)}

### 神经网络分类报告
{classification_report(y_test, y_pred_mlp, target_names=chinese_labels)}

## 结论
1. 神经网络模型在音乐流派分类任务中表现优于KNN算法
2. 两种模型都能够有效区分不同的音乐流派
3. 特征标准化对模型性能有重要影响

## 生成的文件
- 中文实验结果/模型性能对比.png
- 中文实验结果/KNN混淆矩阵.png
- 中文实验结果/神经网络混淆矩阵.png
"""
    
    with open('中文实验结果/实验报告.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("实验报告已保存到: 中文实验结果/实验报告.md")

def main():
    """主函数"""
    print("=" * 60)
    print("音乐流派分类实验（中文版）")
    print("=" * 60)
    
    # 1. 加载和预处理数据
    X, y, le, genre_chinese = load_and_prepare_data()
    
    # 2. 数据划分和标准化
    X_train, X_test, y_train, y_test, scaler = split_and_scale_data(X, y)
    
    # 3. 训练KNN模型
    knn_model, y_pred_knn, knn_accuracy = train_knn_model(X_train, X_test, y_train, y_test)
    
    # 4. 训练神经网络模型
    mlp_model, y_pred_mlp, mlp_accuracy = train_mlp_model(X_train, X_test, y_train, y_test)
    
    # 5. 生成可视化结果
    generate_results_visualization(y_test, y_pred_knn, y_pred_mlp, le, genre_chinese,
                                 knn_accuracy, mlp_accuracy)
    
    # 6. 生成详细报告
    generate_detailed_report(y_test, y_pred_knn, y_pred_mlp, le, genre_chinese,
                           knn_accuracy, mlp_accuracy)
    
    print("\n" + "=" * 60)
    print("实验完成！")
    print("=" * 60)
    print("结果文件保存在 中文实验结果/ 目录中")
    print(f"KNN准确率: {knn_accuracy:.4f} ({knn_accuracy*100:.2f}%)")
    print(f"神经网络准确率: {mlp_accuracy:.4f} ({mlp_accuracy*100:.2f}%)")
    print(f"性能提升: {(mlp_accuracy - knn_accuracy)*100:.2f}个百分点")
    
    # 显示流派中文名称
    chinese_labels = [genre_chinese.get(cls, cls) for cls in le.classes_]
    print(f"分类的音乐流派: {', '.join(chinese_labels)}")

if __name__ == "__main__":
    main()
