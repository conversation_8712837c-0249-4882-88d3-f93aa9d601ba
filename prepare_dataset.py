#!/usr/bin/env python3
"""
数据集准备脚本
用于帮助用户准备GTZAN数据集的本地版本
"""

import os
import shutil
import zipfile
import urllib.request
from pathlib import Path

def create_directory_structure():
    """创建必要的目录结构"""
    print("创建目录结构...")
    
    # 创建数据目录
    data_dir = Path("data")
    data_subdir = data_dir / "Data"
    
    data_dir.mkdir(exist_ok=True)
    data_subdir.mkdir(exist_ok=True)
    
    print(f"✓ 创建目录：{data_dir}")
    print(f"✓ 创建目录：{data_subdir}")
    
    return data_dir, data_subdir

def check_existing_files(data_subdir):
    """检查已存在的文件"""
    print("\n检查现有文件...")
    
    required_files = [
        "features_30_sec.csv",
        "features_3_sec.csv"
    ]
    
    existing_files = []
    missing_files = []
    
    for filename in required_files:
        file_path = data_subdir / filename
        if file_path.exists():
            print(f"✓ 找到：{file_path}")
            existing_files.append(filename)
        else:
            print(f"✗ 缺失：{file_path}")
            missing_files.append(filename)
    
    return existing_files, missing_files

def download_sample_data(data_subdir):
    """创建示例数据文件（用于测试）"""
    print("\n创建示例数据文件...")
    
    # 创建示例的features_30_sec.csv
    sample_30_sec = data_subdir / "features_30_sec.csv"
    sample_3_sec = data_subdir / "features_3_sec.csv"
    
    # CSV头部（包含所有58个特征）
    header = [
        "chroma_stft_mean", "chroma_stft_var", "rms_mean", "rms_var",
        "spectral_centroid_mean", "spectral_centroid_var", "spectral_bandwidth_mean", "spectral_bandwidth_var",
        "rolloff_mean", "rolloff_var", "zero_crossing_rate_mean", "zero_crossing_rate_var",
        "harmony_mean", "harmony_var", "perceptr_mean", "perceptr_var", "tempo",
        "mfcc1_mean", "mfcc1_var", "mfcc2_mean", "mfcc2_var", "mfcc3_mean", "mfcc3_var",
        "mfcc4_mean", "mfcc4_var", "mfcc5_mean", "mfcc5_var", "mfcc6_mean", "mfcc6_var",
        "mfcc7_mean", "mfcc7_var", "mfcc8_mean", "mfcc8_var", "mfcc9_mean", "mfcc9_var",
        "mfcc10_mean", "mfcc10_var", "mfcc11_mean", "mfcc11_var", "mfcc12_mean", "mfcc12_var",
        "mfcc13_mean", "mfcc13_var", "mfcc14_mean", "mfcc14_var", "mfcc15_mean", "mfcc15_var",
        "mfcc16_mean", "mfcc16_var", "mfcc17_mean", "mfcc17_var", "mfcc18_mean", "mfcc18_var",
        "mfcc19_mean", "mfcc19_var", "mfcc20_mean", "mfcc20_var", "label"
    ]
    
    # 音乐流派
    genres = ['blues', 'classical', 'country', 'disco', 'hiphop', 
              'jazz', 'metal', 'pop', 'reggae', 'rock']
    
    import numpy as np
    import pandas as pd
    
    # 生成示例数据（1000个样本，每个流派100个）
    np.random.seed(42)
    
    data_30_sec = []
    data_3_sec = []
    
    for i, genre in enumerate(genres):
        for j in range(100):
            # 为每个流派生成特征向量
            features = np.random.randn(58)  # 58个特征
            features[-1] = genre  # 最后一列是标签
            
            data_30_sec.append(features)
            
            # 3秒数据通常有更多样本（每30秒音频分割成10个3秒片段）
            for k in range(10):
                features_3sec = np.random.randn(58)
                features_3sec[-1] = genre
                data_3_sec.append(features_3sec)
    
    # 创建DataFrame并保存
    df_30_sec = pd.DataFrame(data_30_sec, columns=header)
    df_3_sec = pd.DataFrame(data_3_sec, columns=header)
    
    df_30_sec.to_csv(sample_30_sec, index=False)
    df_3_sec.to_csv(sample_3_sec, index=False)
    
    print(f"✓ 创建示例文件：{sample_30_sec} ({len(df_30_sec)}行)")
    print(f"✓ 创建示例文件：{sample_3_sec} ({len(df_3_sec)}行)")
    
    return True

def show_download_instructions():
    """显示下载说明"""
    print("\n" + "="*60)
    print("GTZAN数据集下载说明")
    print("="*60)
    
    print("\n如果您想使用真实的GTZAN数据集，请按照以下步骤：")
    print("\n1. 下载数据集：")
    print("   方法1：从Kaggle下载")
    print("   - 访问：https://www.kaggle.com/datasets/andradaolteanu/gtzan-dataset-music-genre-classification")
    print("   - 点击 'Download' 按钮")
    print("   - 解压下载的zip文件")
    
    print("\n   方法2：从官方网站下载")
    print("   - 访问：http://marsyas.info/downloads/datasets.html")
    print("   - 下载GTZAN数据集")
    
    print("\n2. 文件放置：")
    print("   将以下文件复制到 data/Data/ 目录：")
    print("   - features_30_sec.csv")
    print("   - features_3_sec.csv")
    
    print("\n3. 验证：")
    print("   重新运行此脚本验证文件是否正确放置")
    
    print("\n注意：")
    print("   - 确保CSV文件包含正确的列名和数据格式")
    print("   - features_30_sec.csv应该有约1000行数据")
    print("   - features_3_sec.csv应该有约10000行数据")

def main():
    """主函数"""
    print("GTZAN数据集准备工具")
    print("="*40)
    
    # 创建目录结构
    data_dir, data_subdir = create_directory_structure()
    
    # 检查现有文件
    existing_files, missing_files = check_existing_files(data_subdir)
    
    if not missing_files:
        print("\n✓ 所有必要的数据文件都已存在！")
        print("您可以直接运行音乐分类实验了。")
        return
    
    print(f"\n缺失 {len(missing_files)} 个文件：{missing_files}")
    
    # 询问用户选择
    print("\n请选择操作：")
    print("1. 创建示例数据文件（用于测试代码）")
    print("2. 显示真实数据集下载说明")
    print("3. 退出")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == '1':
        if download_sample_data(data_subdir):
            print("\n✓ 示例数据文件创建成功！")
            print("注意：这些是随机生成的示例数据，仅用于测试代码功能。")
            print("如需真实实验结果，请使用真实的GTZAN数据集。")
    elif choice == '2':
        show_download_instructions()
    elif choice == '3':
        print("退出")
    else:
        print("无效选择")
    
    print("\n完成！")

if __name__ == "__main__":
    main()
